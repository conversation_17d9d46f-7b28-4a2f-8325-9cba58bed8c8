import {
  mobile,
  backend,
  creator,
  web,
  javascript,
  typescript,
  html,
  css,
  reactjs,
  redux,
  tailwind,
  nodejs,
  mongodb,
  git,
  figma,
  docker,
  threejs,
  carrierguidance,
  eplq,
  gymify,
  medconnectai,
  portfolio,
  resumebuilder,
  turfbooking,
} from "../assets";

export const navLinks = [
  {
    id: "hero",
    title: "Home",
  },
  {
    id: "about",
    title: "About",
  },
  {
    id: "tech",
    title: "Tech",
  },
  {
    id: "work",
    title: "Projects",
  },
  {
    id: "quotes",
    title: "Quotes",
  },
  {
    id: "contact",
    title: "Contact",
  },
];

const services = [
  {
    title: "FullStack Developer",
    icon: web,
  },
  {
    title: "React Native Developer",
    icon: mobile,
  },
  {
    title: "Backend Developer",
    icon: backend,
  },
  {
    title: "Android Developer",
    icon: creator,
  },
];

const technologies = [
  {
    name: "HTML 5",
    icon: html,
  },
  {
    name: "CSS 3",
    icon: css,
  },
  {
    name: "JavaScript",
    icon: javascript,
  },
  {
    name: "TypeScript",
    icon: typescript,
  },
  {
    name: "React JS",
    icon: reactjs,
  },
  {
    name: "Redux Toolkit",
    icon: redux,
  },
  {
    name: "Tailwind CSS",
    icon: tailwind,
  },
  {
    name: "Node JS",
    icon: nodejs,
  },
  {
    name: "MongoDB",
    icon: mongodb,
  },
  {
    name: "Three JS",
    icon: threejs,
  },
  {
    name: "git",
    icon: git,
  },
  {
    name: "figma",
    icon: figma,
  },
  {
    name: "docker",
    icon: docker,
  },
];




const projects = [
  {
    name: "Medconnect-AI",
    description:
      "A full-stack telemedicine system where users can consult with doctors. Features an AI consultation system where users don't need to wait for appointments - they can consult in real-time with AI for accurate results.",
    tags: [
      {
        name: "mongodb",
        color: "green-text-gradient",
      },
      {
        name: "express",
        color: "pink-text-gradient",
      },
      {
        name: "react",
        color: "blue-text-gradient",
      },
      {
        name: "nodejs",
        color: "green-text-gradient",
      },
    ],
    image: medconnectai,
    source_code_link: "https://github.com/sree0077/Medconnect-ai.git",
  },
  {
    name: "Career Guidance",
    description:
      "Education Journey Launchpad is a modern, full-stack web application designed to bridge the gap between students and educational institutions. The platform provides intelligent career guidance, comprehensive aptitude testing, and seamless college application management.",
    tags: [
      {
        name: "firebase",
        color: "blue-text-gradient",
      },
      {
        name: "react",
        color: "green-text-gradient",
      },
      {
        name: "nodejs",
        color: "pink-text-gradient",
      },
      {
        name: "docker",
        color: "blue-text-gradient",
      },
    ],
    image: carrierguidance,
    source_code_link: "https://github.com/sree0077/Educonnect-carrier-guidence.git",
  },
  {
    name: "EPLQ",
    description:
      "EPLQ (Efficient Privacy-Preserving Location-Based Query) is a secure system for handling geospatial data using predicate-only encryption. This system allows users to perform location-based queries while maintaining the privacy and security of sensitive location data.",
    tags: [
      {
        name: "mongodb",
        color: "green-text-gradient",
      },
      {
        name: "express",
        color: "pink-text-gradient",
      },
      {
        name: "react",
        color: "blue-text-gradient",
      },
      {
        name: "nodejs",
        color: "green-text-gradient",
      },
    ],
    image: eplq,
    source_code_link: "https://github.com/sree0077/EPLQ-location-based-query.git",
  },
  {
    name: "Gymify",
    description:
      "An Android application for fitness where users can login and start specific exercises by watching instructions and tutorials, and track their fitness data.",
    tags: [
      {
        name: "kotlin",
        color: "blue-text-gradient",
      },
      {
        name: "java",
        color: "pink-text-gradient",
      },
      {
        name: "android",
        color: "green-text-gradient",
      },
    ],
    image: gymify,
    source_code_link: "https://github.com/",
  },
  {
    name: "Portfolio",
    description:
      "A portfolio website developed using Next.js and deployed on Vercel. Features modern design, 3D animations, and responsive layout to showcase projects and skills.",
    tags: [
      {
        name: "nextjs",
        color: "blue-text-gradient",
      },
      {
        name: "threejs",
        color: "green-text-gradient",
      },
      {
        name: "tailwind",
        color: "pink-text-gradient",
      },
    ],
    image: portfolio,
    source_code_link: "https://github.com/sree0077/Nextjs-portfolio.git",
  },
  {
    name: "Turf Booking",
    description:
      "A website for booking play spaces at the nearest turf facilities. Users can search for available turfs, check pricing, and make reservations for sports activities.",
    tags: [
      {
        name: "python",
        color: "blue-text-gradient",
      },
      {
        name: "flask",
        color: "green-text-gradient",
      },
      {
        name: "mysql",
        color: "pink-text-gradient",
      },
    ],
    image: turfbooking,
    source_code_link: "https://github.com/",
  },
  {
    name: "Resume Builder",
    description:
      "A website where users can enter their details and download resume templates. Features multiple professional templates and real-time preview functionality.",
    tags: [
      {
        name: "php",
        color: "blue-text-gradient",
      },
      {
        name: "mysql",
        color: "green-text-gradient",
      },
      {
        name: "javascript",
        color: "pink-text-gradient",
      },
    ],
    image: resumebuilder,
    source_code_link: "https://github.com/",
  },
];

const testimonials = [
  {
    testimonial:
      "I thought it was impossible to make a website as beautiful as our product, but Rick proved me wrong.",
    name: "Sara Lee",
    designation: "CFO",
    company: "Acme Co",
    image: "https://randomuser.me/api/portraits/women/4.jpg",
  },
  {
    testimonial:
      "I've never met a web developer who truly cares about their clients' success like Rick does.",
    name: "Chris Brown",
    designation: "COO",
    company: "DEF Corp",
    image: "https://randomuser.me/api/portraits/men/5.jpg",
  },
  {
    testimonial:
      "After Rick optimized our website, our traffic increased by 50%. We can't thank them enough!",
    name: "Lisa Wang",
    designation: "CTO",
    company: "456 Enterprises",
    image: "https://randomuser.me/api/portraits/women/6.jpg",
  },
];

const techQuotes = [
  {
    quote: "The best way to predict the future is to invent it.",
    author: "Alan Kay",
    title: "Computer Scientist",
    company: "Xerox PARC",
  },
  {
    quote: "Innovation distinguishes between a leader and a follower.",
    author: "Steve Jobs",
    title: "Co-founder",
    company: "Apple Inc.",
  },
  {
    quote: "Technology is best when it brings people together.",
    author: "Matt Mullenweg",
    title: "Founder",
    company: "WordPress",
  },
  {
    quote: "The advance of technology is based on making it fit in so that you don't really even notice it, so it's part of everyday life.",
    author: "Bill Gates",
    title: "Co-founder",
    company: "Microsoft",
  },
  {
    quote: "Any sufficiently advanced technology is indistinguishable from magic.",
    author: "Arthur C. Clarke",
    title: "Science Fiction Writer & Futurist",
    company: null,
  },
  {
    quote: "The Internet is becoming the town square for the global village of tomorrow.",
    author: "Bill Gates",
    title: "Co-founder",
    company: "Microsoft",
  },
  {
    quote: "Code is like humor. When you have to explain it, it's bad.",
    author: "Cory House",
    title: "Software Architect",
    company: null,
  },
  {
    quote: "The only way to make sense out of change is to plunge into it, move with it, and join the dance.",
    author: "Alan Watts",
    title: "Philosopher & Technology Thinker",
    company: null,
  },
  {
    quote: "Software is a great combination between artistry and engineering.",
    author: "Bill Gates",
    title: "Co-founder",
    company: "Microsoft",
  },
  {
    quote: "The computer was born to solve problems that did not exist before.",
    author: "Bill Gates",
    title: "Co-founder",
    company: "Microsoft",
  },
];

export { services, technologies, projects, testimonials, techQuotes };
