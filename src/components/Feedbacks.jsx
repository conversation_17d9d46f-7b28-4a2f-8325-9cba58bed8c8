import { motion } from "framer-motion";

import { styles } from "../styles";
import { SectionWrapper } from "../hoc";
import { fadeIn, textVariant } from "../utils/motion";
import { techQuotes } from "../constants";

const QuoteCard = ({
  index,
  quote,
  author,
  title,
  company,
}) => (
  <motion.div
    variants={fadeIn("", "spring", index * 0.5, 0.75)}
    className='bg-black-200 p-5 sm:p-6 md:p-8 lg:p-10 rounded-3xl w-[280px] sm:w-[320px] md:w-[350px] lg:w-[380px] xl:w-[400px] flex-shrink-0 h-auto scroll-snap-align-start'
  >
    <p className='text-white font-black text-[36px] sm:text-[42px] lg:text-[48px] leading-none'>"</p>

    <div className='mt-2'>
      <div className='min-h-[120px] sm:min-h-[140px] lg:min-h-[160px] flex items-start'>
        <p className='text-white tracking-wider text-[16px] sm:text-[17px] lg:text-[18px] leading-[24px] sm:leading-[26px] lg:leading-[28px] line-clamp-5 sm:line-clamp-6'>
          {quote}
        </p>
      </div>

      <div className='mt-6 sm:mt-7 flex justify-between items-center gap-2'>
        <div className='flex-1 flex flex-col min-w-0'>
          <p className='text-white font-medium text-[14px] sm:text-[15px] lg:text-[16px] truncate'>
            <span className='blue-text-gradient'>—</span> {author}
          </p>
          <p className='mt-1 text-secondary text-[11px] sm:text-[12px] leading-tight truncate'>
            {title}{company && `, ${company}`}
          </p>
        </div>

        <div className='w-8 h-8 sm:w-9 sm:h-9 lg:w-10 lg:h-10 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center flex-shrink-0'>
          <span className='text-white font-bold text-[12px] sm:text-[13px] lg:text-[14px]'>
            {author.split(' ').map(name => name[0]).join('').slice(0, 2)}
          </span>
        </div>
      </div>
    </div>
  </motion.div>
);

const TechQuotes = () => {
  return (
    <div className={`mt-12 bg-black-100 rounded-[20px]`}>
      <div
        className={`bg-tertiary rounded-2xl ${styles.padding} min-h-[300px]`}
      >
        <motion.div variants={textVariant()}>
          <p className={styles.sectionSubText}>Words of wisdom</p>
          <h2 className={styles.sectionHeadText}>Tech Inspiration.</h2>
        </motion.div>
      </div>
      <div className={`-mt-20 pb-14 ${styles.paddingX}`}>
        {/* Horizontal scroll for all screen sizes with responsive gaps */}
        <div className='overflow-x-auto pb-6'>
          <div className='flex gap-4 sm:gap-6 lg:gap-7 min-w-max px-2 sm:px-0'>
            {techQuotes.map((quote, index) => (
              <QuoteCard key={`${quote.author}-${index}`} index={index} {...quote} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SectionWrapper(TechQuotes, "");
